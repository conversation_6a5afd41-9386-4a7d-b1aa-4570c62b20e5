<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live2D桌宠</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: transparent;
            overflow: hidden;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        #live2d-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }
        
        .debug-panel {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            display: none;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <canvas id="live2d-canvas"></canvas>
    <div class="loading" id="loading">正在加载Live2D模型...</div>
    <div class="debug-panel" id="debug-panel">
        <div>FPS: <span id="fps">0</span></div>
        <div>表情: <span id="current-expression">默认</span></div>
        <div>动作: <span id="current-motion">待机</span></div>
    </div>

    <!-- Live2D Web SDK -->
    <script src="https://cdn.jsdelivr.net/npm/pixi.js@7.2.4/dist/pixi.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/live2d-widget@3.1.4/lib/L2Dwidget.min.js"></script>
    
    <!-- Qt WebChannel -->
    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>

    <script>
        class Live2DManager {
            constructor() {
                this.app = null;
                this.model = null;
                this.bridge = null;
                this.isReady = false;
                this.currentExpression = '默认';
                this.currentMotion = '待机';
                this.fps = 0;
                
                this.init();
            }

            async init() {
                try {
                    console.log('🎭 初始化Live2D管理器...');
                    
                    // 初始化PIXI应用
                    await this.initPixiApp();
                    
                    // 建立WebChannel连接
                    await this.setupWebChannel();
                    
                    // 加载默认模型
                    await this.loadDefaultModel();
                    
                    // 启动渲染循环
                    this.startRenderLoop();
                    
                    console.log('✅ Live2D管理器初始化完成');
                    
                } catch (error) {
                    console.error('❌ Live2D初始化失败:', error);
                    this.showError('Live2D初始化失败: ' + error.message);
                }
            }

            async initPixiApp() {
                const canvas = document.getElementById('live2d-canvas');
                
                this.app = new PIXI.Application({
                    view: canvas,
                    width: window.innerWidth,
                    height: window.innerHeight,
                    backgroundColor: 0x000000,
                    backgroundAlpha: 0, // 透明背景
                    antialias: true,
                    resolution: window.devicePixelRatio || 1,
                    autoDensity: true
                });

                // 响应窗口大小变化
                window.addEventListener('resize', () => {
                    this.app.renderer.resize(window.innerWidth, window.innerHeight);
                });

                console.log('✅ PIXI应用初始化完成');
            }

            async setupWebChannel() {
                return new Promise((resolve, reject) => {
                    if (typeof qt !== 'undefined' && qt.webChannelTransport) {
                        new QWebChannel(qt.webChannelTransport, (channel) => {
                            this.bridge = channel.objects.live2dBridge;
                            
                            if (this.bridge) {
                                // 注册回调函数
                                this.bridge.modelLoadRequested.connect(this.loadModel.bind(this));
                                this.bridge.expressionChangeRequested.connect(this.setExpression.bind(this));
                                this.bridge.motionPlayRequested.connect(this.playMotion.bind(this));
                                this.bridge.debugModeChanged.connect(this.toggleDebug.bind(this));
                                
                                // 通知Qt端已准备就绪
                                this.bridge.notifyReady();
                                
                                console.log('✅ WebChannel连接成功');
                                resolve();
                            } else {
                                reject(new Error('无法获取Live2D桥接对象'));
                            }
                        });
                    } else {
                        // 开发模式，无WebChannel
                        console.log('⚠️ 开发模式：无WebChannel连接');
                        resolve();
                    }
                });
            }

            async loadDefaultModel() {
                // 这里可以加载默认的Live2D模型
                // 暂时显示占位符
                this.showPlaceholder();
                this.hideLoading();
            }

            showPlaceholder() {
                // 创建一个简单的占位符图形
                const graphics = new PIXI.Graphics();
                graphics.beginFill(0x3498db, 0.3);
                graphics.drawRoundedRect(0, 0, 200, 300, 20);
                graphics.endFill();
                
                graphics.beginFill(0x2c3e50);
                graphics.drawCircle(100, 80, 30); // 头部
                graphics.endFill();
                
                graphics.beginFill(0xe74c3c);
                graphics.drawCircle(85, 75, 5); // 左眼
                graphics.drawCircle(115, 75, 5); // 右眼
                graphics.endFill();
                
                graphics.beginFill(0xe67e22);
                graphics.drawRoundedRect(85, 90, 30, 8, 4); // 嘴巴
                graphics.endFill();
                
                // 居中显示
                graphics.x = (this.app.screen.width - 200) / 2;
                graphics.y = (this.app.screen.height - 300) / 2;
                
                // 添加交互
                graphics.interactive = true;
                graphics.buttonMode = true;
                graphics.on('pointerdown', () => {
                    this.onModelClick();
                });
                
                this.app.stage.addChild(graphics);
                this.model = graphics;
                
                console.log('✅ 占位符模型已加载');
            }

            async loadModel(modelPath) {
                try {
                    console.log('🎭 加载Live2D模型:', modelPath);
                    
                    // 这里实现真正的Live2D模型加载
                    // 使用Live2D Web SDK加载.model3.json文件
                    
                    this.showLoading();
                    
                    // 模拟加载过程
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    
                    this.hideLoading();
                    this.currentExpression = '默认';
                    this.currentMotion = '待机';
                    
                    // 通知Qt端加载完成
                    if (this.bridge) {
                        this.bridge.notifyModelLoaded(modelPath);
                    }
                    
                    console.log('✅ 模型加载完成');
                    
                } catch (error) {
                    console.error('❌ 模型加载失败:', error);
                    this.showError('模型加载失败: ' + error.message);
                }
            }

            setExpression(expressionName, intensity = 1.0) {
                console.log(`🎭 设置表情: ${expressionName}, 强度: ${intensity}`);
                
                this.currentExpression = expressionName;
                
                // 这里实现表情变化的视觉效果
                if (this.model && this.model instanceof PIXI.Graphics) {
                    // 简单的颜色变化示例
                    const colors = {
                        '开心': 0xf1c40f,
                        '难过': 0x3498db,
                        '生气': 0xe74c3c,
                        '惊讶': 0x9b59b6,
                        '默认': 0x3498db
                    };
                    
                    const color = colors[expressionName] || colors['默认'];
                    this.model.tint = color;
                }
                
                // 通知Qt端表情已变化
                if (this.bridge) {
                    this.bridge.notifyExpressionChanged(expressionName, intensity);
                }
            }

            playMotion(motionName, priority = 1) {
                console.log(`🎬 播放动作: ${motionName}, 优先级: ${priority}`);
                
                this.currentMotion = motionName;
                
                // 这里实现动作播放的视觉效果
                if (this.model) {
                    // 简单的缩放动画示例
                    const originalScale = this.model.scale.x;
                    this.model.scale.set(originalScale * 1.1);
                    
                    setTimeout(() => {
                        if (this.model) {
                            this.model.scale.set(originalScale);
                        }
                    }, 300);
                }
                
                // 通知Qt端动作已开始
                if (this.bridge) {
                    this.bridge.notifyMotionStarted(motionName);
                }
            }

            onModelClick() {
                console.log('🖱️ 模型被点击');
                
                // 随机播放表情
                const expressions = ['开心', '难过', '生气', '惊讶', '默认'];
                const randomExpression = expressions[Math.floor(Math.random() * expressions.length)];
                this.setExpression(randomExpression);
                
                // 通知Qt端模型被点击
                if (this.bridge) {
                    this.bridge.notifyModelClicked();
                }
            }

            startRenderLoop() {
                let lastTime = performance.now();
                let frameCount = 0;
                
                const updateFPS = () => {
                    const now = performance.now();
                    frameCount++;
                    
                    if (now - lastTime >= 1000) {
                        this.fps = Math.round(frameCount * 1000 / (now - lastTime));
                        frameCount = 0;
                        lastTime = now;
                        
                        this.updateDebugPanel();
                    }
                };
                
                this.app.ticker.add(updateFPS);
                console.log('✅ 渲染循环已启动');
            }

            toggleDebug(enabled) {
                const debugPanel = document.getElementById('debug-panel');
                debugPanel.style.display = enabled ? 'block' : 'none';
            }

            updateDebugPanel() {
                document.getElementById('fps').textContent = this.fps;
                document.getElementById('current-expression').textContent = this.currentExpression;
                document.getElementById('current-motion').textContent = this.currentMotion;
            }

            showLoading() {
                document.getElementById('loading').style.display = 'block';
            }

            hideLoading() {
                document.getElementById('loading').style.display = 'none';
            }

            showError(message) {
                const loading = document.getElementById('loading');
                loading.textContent = '❌ ' + message;
                loading.style.color = '#e74c3c';
                loading.style.display = 'block';
            }
        }

        // 启动Live2D管理器
        window.live2dManager = new Live2DManager();
        
        // 全局函数供Qt调用
        window.setExpression = (name, intensity) => window.live2dManager.setExpression(name, intensity);
        window.playMotion = (name, priority) => window.live2dManager.playMotion(name, priority);
        window.loadModel = (path) => window.live2dManager.loadModel(path);
        
    </script>
</body>
</html>
